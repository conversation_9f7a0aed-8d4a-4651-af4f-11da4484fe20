import { use<PERSON><PERSON>back, useRef, useState } from 'react'
import type { FlowGraph, FlowNodeData, NodeChildRef, NodeRunResult, NodeRuntimeContext } from './types'
import { getNode } from './registry'
import './autoload'
import { useCampaignData } from '../hooks/useCampaignStore'
import { useFlowRuntimeBridge } from './FlowRuntimeContext'
import { getConnectedEdges } from './utils/graphAnalysis'

type RunOptions = {
    debug?: boolean
    /** Optional maximum steps to avoid infinite loops. Defaults to 1000. */
    maxSteps?: number
}

function toArray<T>(value: T | T[] | undefined | null): T[] {
    if (value == null) return []
    return Array.isArray(value) ? value : [value]
}

function resolveChildren(graph: FlowGraph, nodeId: string): NodeChildRef[] {
    const edges = (graph.edges ?? [])
    const nodes = (graph.nodes ?? [])
    const outgoing = edges.filter((e: any) => e.source === nodeId)
    
    // Only include edges that point to nodes that actually exist in the graph
    return outgoing
        .filter((e: any) => nodes.some((n: any) => n.id === e.target))
        .map((e: any) => ({ port: e.sourceHandle ?? 'output', nodeId: e.target }))
}

function findStartNodes(graph: FlowGraph) {
    const nodes = (graph.nodes ?? [])
    return nodes.filter((n: any) => (n?.data as FlowNodeData)?.nodeType.startsWith('trigger:'))
}

export function useFlowEngine() {
    const [running, setRunning] = useState(false)
    const [currentNodeId, setCurrentNodeId] = useState<string | null>(null)
    const cancelFlagRef = useRef(false)
    const runningRef = useRef(false)

    const { campaignData } = useCampaignData()
    const flows = campaignData?.campaign?.config?.flows

    const bridge = useFlowRuntimeBridge()


    const cancel = useCallback(() => {
        cancelFlagRef.current = true
    }, [])

    const getFlowById = useCallback((flowId: string) => {
        return flows.find((f) => f.id === flowId)
    }, [flows])

    const runFlow = useCallback(async (flowId: string, payload?: any, options?: RunOptions) => {
        console.log("runFlow", flowId, payload)

        if (runningRef.current) return
        runningRef.current = true
        setRunning(true)
        cancelFlagRef.current = false

        const debug = Boolean(options?.debug)
        const maxSteps = options?.maxSteps ?? 1000

        try {
            const graph = getFlowById(flowId)
            if (!graph) {
                throw new Error(`Flow not found: ${flowId}`)
            }

            // determine entry points
            const startNodes = findStartNodes(graph)
            if (startNodes.length === 0) {
                throw new Error(`No start node (trigger:OnFlowStart) found in flow ${flowId}`)
            }

            console.log("startNodes", startNodes)
            
            // traversal queue
            const queue: string[] = startNodes.map((n: any) => n.id)
            const visitedCount = new Map<string, number>()
            let steps = 0


            // Base context without node-specific edges
            const baseCtx = {
                graph,
                cancelToken: { isCancelled: () => cancelFlagRef.current },
                setSceneById: bridge?.setSceneById,
                setGameScene: bridge?.setGameScene,
                payload,
            }

            while (queue.length > 0) {
                if (cancelFlagRef.current) {
                    console.log('cancelFlagRef.current', cancelFlagRef.current)
                    break

                }
                if (steps++ > maxSteps) {
                    // eslint-disable-next-line no-console
                    console.warn(`useFlowEngine: exceeded max steps (${maxSteps}). Aborting to prevent infinite loop.`)
                    break
                }

                const nodeId = queue.shift() as string
                setCurrentNodeId(nodeId)

                const node = (graph.nodes as any[]).find((n) => n.id === nodeId)
                if (!node) {
                    console.log('node not found', nodeId, graph)
                    continue
                }
                const data = (node.data ?? {}) as FlowNodeData
                const def = getNode(data.nodeType)
                const children = resolveChildren(graph, nodeId)

                // Get edges for this specific node
                const { inputEdges, outputEdges } = getConnectedEdges(graph, nodeId)

                // Create node-specific context with edges
                const ctx: NodeRuntimeContext = {
                    ...baseCtx,
                    inputEdges,
                    outputEdges,
                }

                console.log('children ', children)
                console.log('flow: ', graph)


                    console.log('[FlowEngine] run node', { nodeId, type: data.nodeType, children })

                let result: NodeRunResult = { next: children }
                try {
                    if (def?.runtime?.run) {
                        result = await def.runtime.run({ ctx, settings: (data.settings ?? {}) as any, inputs: undefined, children })
                        console.log('result', result)
                    }
                } catch (err) {
                    // eslint-disable-next-line no-console
                    console.error('useFlowEngine: node runtime error', err)
                    // stop on errors for now
                    break
                }

                const nextRefs = toArray(result.next)
                const nextNodeIds = nextRefs.map((r) => r.nodeId).filter(Boolean) as string[]


                // prevent unlimited revisits, allow a few passes through cycles
                const currentVisits = (visitedCount.get(nodeId) ?? 0) + 1
                visitedCount.set(nodeId, currentVisits)

                for (const nextId of nextNodeIds) {
                    const nextVisits = visitedCount.get(nextId) ?? 0
                    if (nextVisits > 200) {
                        console.warn(`useFlowEngine: node ${nextId} visited too many times; skipping`)
                        continue
                    }
                    queue.push(nextId)
                }
                console.log('queue', queue)
            }
        } finally {
            runningRef.current = false
            setRunning(false)
            setCurrentNodeId(null)
        }
    }, [getFlowById])

    return {
        runFlow,
        running,
        cancel,
        currentNodeId,
    }
}


