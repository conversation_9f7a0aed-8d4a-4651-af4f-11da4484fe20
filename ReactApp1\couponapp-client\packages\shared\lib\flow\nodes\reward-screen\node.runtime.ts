import type { NodeRuntimeContext, NodeRunResult, NodeChildRef } from '../../types'
import type { Settings } from './node.types'

export async function run({ ctx, settings, children }: { ctx: NodeRuntimeContext; settings: Settings; children?: NodeChildRef[] }): Promise<NodeRunResult> {
	const edge = ctx.inputEdges[0]
	
	if(!edge) {
		console.log("No edge found. Not showing reward screen.")
		return { next: [] }
	}

	const screenId = "custom/node/" + edge.id
	console.log("Open screen: ", screenId)

	ctx.setGameScene(ctx.graph.parentWidgetId, screenId)
	
	return { next: children }
}



