import React, { createContext, useContext, useState, useEffect, useCallback, useRef, ReactNode, useMemo } from 'react'
import { useImmer, Updater } from 'use-immer'
import { toast } from '@repo/shared/components/ui/use-toast'
import { useFetch, useApiMutation, put } from '@repo/shared/lib/hooks/useApi'
import { CampaignData, CampaignDomainSettingsDto, CampaignEditorDto, CampaignSaveMode, CampaignScene, CampaignUpdateDto } from '@repo/shared/lib/types/campaign'
import { createShortUuid, getErrorMessage } from '@repo/shared/lib/utils'
import { TabListTab } from '@repo/shared/components/ui/tablist'
import { CheckIcon, Loader2Icon } from 'lucide-react'
import { debounce } from 'lodash'
import { Widget, WidgetTemplateSchema } from '@repo/shared/lib/types/editor'
// import { CampaignFlow, CampaignFlowNode } from '@repo/shared/lib/campaign/actionNode'
import { useGlobalVariables } from '@repo/shared/lib/hooks/useGlobalVariables'
import { useSceneWidgetsTraverse } from '@repo/shared/lib/hooks/useWidgetHelpers'
import { useHistoryManager } from './historyManager'
import { useAtom } from 'jotai'
import { editorCampaignById } from '@repo/shared/lib/atoms/editor-atoms'
import { FlowGraph } from '@repo/shared/lib/flow/types'
// Types
interface CampaignEditorContextProps {
    data: CampaignEditorDto | null
    isLoading: boolean
    error: any
    viewMode: 'desktop' | 'mobile'
    scenes: CampaignScene[] | null
    // nodes: CampaignFlowNode[]
    flows: FlowGraph[]
    variables: any
    activeTab: 'logic' | 'design'
    campaignSaveState: CampaignSaveState
    campaignDomainSettings: CampaignDomainSettingsDto
    setCampaignDomainSettings: (settings: CampaignDomainSettingsDto) => void
    setActiveTab: (tab: 'logic' | 'design') => void
    setScenes: Updater<CampaignScene[]>
    // setNodes: Updater<CampaignFlowNode[]>
    setFlows: Updater<FlowGraph[]>
    updateVariables: (variables: any) => void
    setViewMode: (mode: 'desktop' | 'mobile') => void
    undo: () => void
    redo: () => void
    clearHistory: () => void
    saveChanges: (saveMode: CampaignSaveMode) => Promise<void>
    setSaveChangesRequested: (requested: boolean) => void
    selectedFlowId: string | undefined
    setSelectedFlowId: (id: string) => void
}

interface CampaignSaveState {
    label: string
    isLoading: boolean
}

// Context
export const CampaignEditorContext = createContext<CampaignEditorContextProps | undefined>(undefined)

// Provider Component
export const CampaignEditorProvider = ({ children, campaignId }: { children: ReactNode; campaignId: string }) => {
    const { data, isLoading, error } = useFetch<CampaignEditorDto>('campaigns/edit/' + campaignId, 'campaign-' + campaignId)


    const [scenes, setScenes] = useImmer<CampaignScene[] | null>(null)
    const [flows, setFlows] = useImmer<FlowGraph[]>([])
    const { variables, loadVariables, updateVariables } = useGlobalVariables()


    const [saveChangesRequested, setSaveChangesRequested] = useState(false)
    const [activeTab, setActiveTab] = useState<'logic' | 'design'>('design')
    const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop')
    
    const updateCampaign = useApiMutation((o: CampaignUpdateDto) => put('campaigns/' + campaignId, o), 'campaign_saved_' + campaignId)
    const lastSavedScenes = useRef<string | null>(null)

    const [campaignDomainSettings, setCampaignDomainSettings] = useState<CampaignDomainSettingsDto>({ customDomainId: '', slug: '' })

    const [campaignSaveState, setSavingState] = useState<CampaignSaveState>({ label: '', isLoading: false })

    const [selectedFlowId, setSelectedFlowId] = useState<string | undefined>(flows[0]?.id)

    const {findAssetIds} = useSceneWidgetsTraverse(scenes)


    const { undo, redo, clearHistory } = useHistoryManager({
        isLoaded: !isLoading && !error && data != null && scenes != null,
        scenes: scenes || [],
        variables,
        setScenes,
        updateVariables,
    })

    // Keep all your existing useEffects and functions here
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.ctrlKey && event.key === 'z') {
                event.preventDefault()
                undo()
            } else if (event.ctrlKey && event.key === 'y') {
                event.preventDefault()
                redo()
            }
        }

        window.addEventListener('keydown', handleKeyDown)
        return () => window.removeEventListener('keydown', handleKeyDown)
    }, [undo, redo])

    useEffect(() => {
        console.log("Reload campaign scenes")
        if (isLoading || error) return

        if (!data || !data.config) {
            setScenes([])
            setFlows([])
            return
        }

        const campaignConfig = data.config
        
        setScenes(campaignConfig.scenes)
        setFlows(campaignConfig.flows || [])
        setCampaignDomainSettings(data.campaignDomainSettings ?? { customDomainId: '', slug: '' })
        loadVariables(campaignConfig.variables)

    }, [data, isLoading, error])

    const saveChanges = useCallback(
        async (saveMode: CampaignSaveMode) => {
            if (!data) return

            const campaignConfig = {
                scenes: scenes || [],
                flows: flows,
                variables: variables,
                revision: (data.config?.revision ?? 0) + 1,
                usedAssetIds: findAssetIds(),
            }

            console.log("Used asset ids: ", findAssetIds())
            try {
                setSavingState({
                    isLoading: true,
                    label: 'Saving changes...',
                })

                const updateDto: CampaignUpdateDto = { saveMode, config: campaignConfig, name: data.name, campaignDomainSettings: campaignDomainSettings }
                 await updateCampaign.mutateAsync(updateDto)

                setSavingState({
                    isLoading: false,
                    label: 'Saved',
                })

                // setTimeout(() => {
                //     setSavingState({
                //         isLoading: false,
                //         label: '',
                //     })
                // }, 4000)
            } catch (error) {
                setSavingState({
                    isLoading: false,
                    label: 'Error while saving',
                })

                toast({
                    title: 'Error',
                    description: 'Failed to save campaign: ' + getErrorMessage(error),
                    variant: 'destructive',
                })
            }
        },
        [campaignId, data,  flows, scenes, updateCampaign, variables, saveChangesRequested, isLoading]
    )

    useEffect(() => {
        if (!saveChangesRequested) return
          saveChanges(CampaignSaveMode.SaveAsDraft)
          setSaveChangesRequested(false)
    }, [saveChangesRequested])

     useEffect(() => {
         const handleKeyDown = (event: KeyboardEvent) => {
             const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0
             const modifier = isMac ? event.metaKey : event.ctrlKey

             if (modifier && event.key.toLowerCase() === 's') {
                  setSaveChangesRequested(true)
                 event.preventDefault()
             }
         }

         window.addEventListener('keydown', handleKeyDown)
         return () => window.removeEventListener('keydown', handleKeyDown)
     }, [])

    // const debouncedSaveChanges = useCallback(
    //     debounce((currentScenes: CampaignScene[] | null, currentFlows: CampaignFlow[], currentVariables: any, isCurrentlyLoading: boolean) => {
    //         const serializedState = JSON.stringify(currentScenes) + JSON.stringify(currentVariables) + JSON.stringify(currentFlows)
    //         if (isCurrentlyLoading) {
    //             return
    //         }
    //         if (lastSavedScenes.current === null) {
    //             lastSavedScenes.current = serializedState
    //             return
    //         }
    //         if (serializedState !== lastSavedScenes.current) {
    //             lastSavedScenes.current = serializedState
    //             setSaveChangesRequested(true)
    //         }
    //     }, 800),
    //     [scenes, flows, variables, isLoading]
    // )

    // useEffect(() => {
    //     debouncedSaveChanges(scenes, flows, variables, isLoading)

    //     return () => {
    //         debouncedSaveChanges.cancel()
    //     }
    // }, [debouncedSaveChanges, scenes, flows, variables, isLoading])

    const contextValue: CampaignEditorContextProps = {
        data,
        isLoading,
        error,
        scenes,
        flows,
        variables,
        activeTab,
        viewMode,
        campaignSaveState,
        campaignDomainSettings,
        setCampaignDomainSettings,
        setActiveTab,
        setScenes,
        setFlows,
        updateVariables,
        setViewMode,
        undo,
        redo,
        clearHistory,
        saveChanges,
        setSaveChangesRequested,
        selectedFlowId,
        setSelectedFlowId,
    }

    return <CampaignEditorContext.Provider value={contextValue}>{children}</CampaignEditorContext.Provider>
}


// Main hook remains for backward compatibility
export const useCampaignEditor = () => {

    // @ts-ignore
    //We are within iframe editor.
    if(window.campaignEditorContext) {
        // @ts-ignore
        return window.campaignEditorContext
    }

    const context = useContext(CampaignEditorContext)
    if (!context) {
        throw new Error('useCampaignEditor must be used within a CampaignEditorProvider')
    }
    return context
}
