import React, { useState } from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps, NodeEditorContext, GameScreenDefinition } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Button } from '@repo/shared/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/shared/components/ui/dialog'

import { Plus, Edit } from 'lucide-react'
import { useGameRewardsDetails, RewardSet } from '@repo/shared/lib/game/useGameRewardsDetails'
import RewardSetManager from '@repo/shared/features/rewards/components/RewardSetManager'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'

function RewardScreenNodeBody(_: NodeEditorBodyProps) {
	return <div className="w-[260px]"></div>
}

function RewardScreenProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
	const [isDialogOpen, setIsDialogOpen] = useState(false)
	const [isCreatingNew, setIsCreatingNew] = useState(false)

	// For now, use a default widget ID. In the future, this should come from flow context
	const gameWidgetId = 'default'
	const { rewardSets, setRewardSets } = useGameRewardsDetails(gameWidgetId)

	const selectedRewardSet = rewardSets.find((set: RewardSet) => set.id === settings?.rewardSetId)



	const handleRewardSetChange = (updatedRewardSet: RewardSet) => {
		const updatedRewardSets = rewardSets.map((set: RewardSet) =>
			set.id === updatedRewardSet.id ? updatedRewardSet : set
		)
		setRewardSets(updatedRewardSets)
	}

	const handleOpenDialog = () => {
		if (!settings?.rewardSetId) {
			setIsCreatingNew(true)
		}
		setIsDialogOpen(true)
	}

	const handleSelectChange = (value: string) => {
		if (value === 'create-new') {
			setIsCreatingNew(true)
			setIsDialogOpen(true)
		} else {
			onChange({ rewardSetId: value })
		}
	}

	return (
		<div className="space-y-2">
			<div className="space-y-1">
				<Label>Reward Set</Label>
				<Select value={settings?.rewardSetId ?? ''} onValueChange={handleSelectChange}>
					<SelectTrigger>
						<SelectValue placeholder="Select reward set" />
					</SelectTrigger>
					<SelectContent>
						{rewardSets.map((set: RewardSet) => (
							<SelectItem key={set.id} value={set.id}>
								{set.name}
							</SelectItem>
						))}
						<SelectItem value="create-new">
							<div className="flex items-center">
								<Plus className="w-4 h-4 mr-2" />
								Create reward set
							</div>
						</SelectItem>
					</SelectContent>
				</Select>
			</div>
			{settings?.rewardSetId && (
				<div className="pt-1">
					<Button
						variant="link"
						size="sm"
						className="px-0"
						onClick={handleOpenDialog}
					>
						<Edit className="w-4 h-4 mr-1" />
						Edit rewards
					</Button>
				</div>
			)}

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="max-w-4xl p-0 overflow-hidden [&>button]:hidden flex flex-col">
					<DialogHeader className="p-6 pb-0">
						<DialogTitle>
							{isCreatingNew ? 'Create Reward Set' : 'Edit Reward Set'}
						</DialogTitle>
					</DialogHeader>


					<div className="flex-1 overflow-hidden">
						{isCreatingNew ? (
							<RewardSetManager
								set={{
									id: `temp-${Date.now()}`,
									name: "Outcome Rewards",
									gameWidgetId,
									rewards: [],
									winMechanics: {
										mode: "attempts",
										baseWinChance: 30,
										enableProgressiveChance: true,
										progressiveChanceIncrease: 5,
										enableGuaranteedWin: true,
										guaranteedWinAttempts: 10,
									}
								}}
								onChange={(updatedSet) => {
									const newRewardSet = { ...updatedSet, id: `reward-set-${Date.now()}` }
									const updatedRewardSets = [...rewardSets, newRewardSet]
									setRewardSets(updatedRewardSets)
									onChange({ rewardSetId: newRewardSet.id })
									setIsCreatingNew(false)
								}}
							/>
						) : selectedRewardSet ? (
								<RewardSetManager
									set={selectedRewardSet}
									onChange={handleRewardSetChange}
								/>
						) : null}
					</div>
					<div className="p-6 pt-0 flex justify-end border-t">
						<Button onClick={() => setIsDialogOpen(false)}>
							Done
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	)
}

const def: NodeDefinition<Settings> = {
	type: 'client:RewardScreen',
	label: 'Game Reward Screen',
	icon: 'GiftIcon',
	inputs: () => [{ key: 'input', acceptsOnly: ['game-outcome'] }],
	outputs: () => [{ key: 'output', kind: 'event' }],
	exposeScreens: (_settings: Settings, ctx: NodeEditorContext): GameScreenDefinition[] => {
		const edge = ctx.inputEdges[0]
		const endLabel = (edge?.data as any)?.endLabel || 'Outcome'
		const screenId = "custom/node/" + edge.id
		const screens = [
			{
				id: screenId,
				name: "Outcome - " + endLabel,
			}
		]
		return screens
	},
	editor: {
		renderNodeBody: RewardScreenNodeBody,
		renderProperties: RewardScreenProperties,
	},
	runtime: { run },
}

registerNode(def)
export default def