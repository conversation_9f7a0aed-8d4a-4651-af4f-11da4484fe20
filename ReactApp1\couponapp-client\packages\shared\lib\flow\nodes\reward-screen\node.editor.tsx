import React, { useState } from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps, NodeEditorContext, GameScreenDefinition } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Button } from '@repo/shared/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/shared/components/ui/dialog'
import { Input } from '@repo/shared/components/ui/input'
import { Plus, Edit } from 'lucide-react'
import { useGameRewardsDetails, RewardSet } from '@repo/shared/lib/game/useGameRewardsDetails'
import RewardSetManager from '@repo/shared/features/rewards/components/RewardSetManager'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'

function RewardScreenNodeBody(_: NodeEditorBodyProps) {
	return <div className="w-[260px]"></div>
}

function RewardScreenProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
	const [isDialogOpen, setIsDialogOpen] = useState(false)
	const [isCreatingNew, setIsCreatingNew] = useState(false)
	const [newRewardSetName, setNewRewardSetName] = useState('')

	// For now, use a default widget ID. In the future, this should come from flow context
	const gameWidgetId = 'default'
	const { rewardSets, setRewardSets } = useGameRewardsDetails(gameWidgetId)

	const selectedRewardSet = rewardSets.find((set: RewardSet) => set.id === settings?.rewardSetId)

	const handleCreateNewRewardSet = () => {
		if (!newRewardSetName.trim()) return

		const newRewardSet: RewardSet = {
			id: `reward-set-${Date.now()}`,
			name: newRewardSetName.trim(),
			gameWidgetId,
			rewards: [],
			winMechanics: {
				mode: "attempts",
				baseWinChance: 30,
				enableProgressiveChance: true,
				progressiveChanceIncrease: 5,
				enableGuaranteedWin: true,
				guaranteedWinAttempts: 10,
			}
		}

		const updatedRewardSets = [...rewardSets, newRewardSet]
		setRewardSets(updatedRewardSets)
		onChange({ rewardSetId: newRewardSet.id })
		setNewRewardSetName('')
		setIsCreatingNew(false)
	}

	const handleRewardSetChange = (updatedRewardSet: RewardSet) => {
		const updatedRewardSets = rewardSets.map((set: RewardSet) =>
			set.id === updatedRewardSet.id ? updatedRewardSet : set
		)
		setRewardSets(updatedRewardSets)
	}

	const handleOpenDialog = () => {
		if (!settings?.rewardSetId) {
			setIsCreatingNew(true)
		}
		setIsDialogOpen(true)
	}

	const handleSelectChange = (value: string) => {
		if (value === '') {
			setIsCreatingNew(true)
			setIsDialogOpen(true)
		} else {
			onChange({ rewardSetId: value })
		}
	}

	return (
		<div className="space-y-2">
			<div className="space-y-1">
				<Label>Reward Set</Label>
				<Select value={settings?.rewardSetId ?? ''} onValueChange={handleSelectChange}>
					<SelectTrigger>
						<SelectValue placeholder="Select reward set" />
					</SelectTrigger>
					<SelectContent>
						
						{rewardSets.map((set: RewardSet) => (
							<SelectItem key={set.id} value={set.id}>
								{set.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>
			<div className="pt-1">
				<Button
					variant="link"
					size="sm"
					className="px-0"
					onClick={handleOpenDialog}
					disabled={!settings?.rewardSetId && !isCreatingNew}
				>
					<Edit className="w-4 h-4 mr-1" />
					{settings?.rewardSetId ? 'Edit reward set' : 'Create reward set'}
				</Button>
			</div>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="max-w-4xl  p-0 overflow-hidden">
					<DialogHeader className="p-6 pb-0">
						<DialogTitle>
						</DialogTitle>
					</DialogHeader>


					{isCreatingNew ? (
						<div className="p-6 space-y-4">
							<div className="space-y-2">
								<Label htmlFor="reward-set-name">Reward Set Name</Label>
								<Input
									id="reward-set-name"
									placeholder="Enter reward set name"
									value={newRewardSetName}
									onChange={(e) => setNewRewardSetName(e.target.value)}
								/>
							</div>
							<div className="flex justify-end gap-2">
								<Button variant="outline" onClick={() => {
									setIsCreatingNew(false)
									setIsDialogOpen(false)
									setNewRewardSetName('')
								}}>
									Cancel
								</Button>
								<Button onClick={handleCreateNewRewardSet} disabled={!newRewardSetName.trim()}>
									Create Reward Set
								</Button>
							</div>
						</div>
					) : selectedRewardSet ? (
							<RewardSetManager
								set={selectedRewardSet}
								onChange={handleRewardSetChange}
							/>
					) : null}
				</DialogContent>
			</Dialog>
		</div>
	)
}

const def: NodeDefinition<Settings> = {
	type: 'client:RewardScreen',
	label: 'Game Reward Screen',
	icon: 'GiftIcon',
	inputs: () => [{ key: 'input', acceptsOnly: ['game-outcome'] }],
	outputs: () => [{ key: 'output', kind: 'event' }],
	exposeScreens: (_settings: Settings, ctx: NodeEditorContext): GameScreenDefinition[] => {
		const edge = ctx.inputEdges[0]
		const endLabel = (edge?.data as any)?.endLabel || 'Outcome'
		const screenId = "custom/node/" + edge.id
		const screens = [
			{
				id: screenId,
				name: "Outcome - " + endLabel,
			}
		]
		return screens
	},
	editor: {
		renderNodeBody: RewardScreenNodeBody,
		renderProperties: RewardScreenProperties,
	},
	runtime: { run },
}

registerNode(def)
export default def