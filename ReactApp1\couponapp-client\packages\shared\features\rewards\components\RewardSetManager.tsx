import React, { useMemo, useState } from 'react'
import type { RewardDefinition, RewardSet as DashboardRewardSet, RewardType as DashboardRewardType } from '../types'
import type { RewardSet as GameRewardSet, Reward as GameReward } from '@repo/shared/lib/game/useGameRewardsDetails'
import { Button } from '@repo/shared/components/ui/button'
import { Input } from '@repo/shared/components/ui/input'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@repo/shared/components/ui/table'
import { Plus, Trash2 } from 'lucide-react'

export interface RewardSetManagerProps {
  set: DashboardRewardSet | GameRewardSet
  onChange?: (next: DashboardRewardSet | GameRewardSet) => void
}

function genId(prefix = 'reward') {
  return `${prefix}-${Math.random().toString(36).slice(2, 8)}`
}

export const RewardSetManager: React.FC<RewardSetManagerProps> = ({ set, onChange }) => {
  const [local, setLocal] = useState<DashboardRewardSet | GameRewardSet>(set)
  const [currentView, setCurrentView] = useState<'list' | 'add'>('list')

  // keep external in sync if parent updates the set prop
  React.useEffect(() => setLocal(set), [set])

  // Check if this is a game reward set (has winMechanics) or dashboard reward set
  const isGameRewardSet = (s: DashboardRewardSet | GameRewardSet): s is GameRewardSet => {
    return 'winMechanics' in s
  }

  const rewardTypes = useMemo(() => {
    return isGameRewardSet(set) ? ['coupon', 'gift_card'] : ['coupon-code', 'claimable-url']
  }, [set])

  const [newReward, setNewReward] = useState<{name: string, description: string, type: string}>({
    name: '',
    description: '',
    type: rewardTypes[0],
  })

  const handleRemove = (id: string) => {
    const next = { ...local, rewards: local.rewards.filter((r: any) => r.id !== id) } as any
    setLocal(next)
    onChange?.(next)
  }

  const handleAdd = () => {
    if (!newReward.name?.trim()) return

    let reward: any
    if (isGameRewardSet(local)) {
      // Game reward format
      reward = {
        id: genId(),
        name: newReward.name.trim(),
        description: newReward.description?.trim() ?? '',
        type: newReward.type as 'coupon' | 'gift_card',
        settings: newReward.type === 'coupon' ? { codes: [] } : { amount: '0', currency: 'USD' },
        dropRate: 100,
      }
    } else {
      // Dashboard reward format
      reward = {
        id: genId(),
        name: newReward.name.trim(),
        description: newReward.description?.trim() ?? '',
        type: newReward.type as DashboardRewardType,
        dropRate: 1.0,
      }
    }

    const next = { ...local, rewards: [...(local.rewards || []), reward] } as any
    setLocal(next)
    onChange?.(next)
    setNewReward({ name: '', description: '', type: rewardTypes[0] })
    setCurrentView('list')
  }

  if (currentView === 'add') {
    return (
      <div className="w-full space-y-6  p-4 flex flex-col h-[600px]">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" size="sm" onClick={() => setCurrentView('list')}>
            ← Back to Rewards
          </Button>
          <div className="text-lg font-medium text-foreground">Add New Reward</div>
        </div>

        <div className="space-y-4 max-w-2xl">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="reward-name" className="text-sm font-medium text-foreground mb-2 block">
                Reward Name
              </Label>
              <Input
                id="reward-name"
                placeholder="Enter reward name"
                value={newReward.name}
                onChange={(e) => setNewReward((s) => ({ ...s, name: e.target.value }))}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="reward-type" className="text-sm font-medium text-foreground mb-2 block">
                Type
              </Label>
              <Select value={newReward.type} onValueChange={(v) => setNewReward((s) => ({ ...s, type: v }))}>
                <SelectTrigger id="reward-type" className="w-full">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {rewardTypes.map((t) => (
                    <SelectItem key={t} value={t}>{t}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <Label htmlFor="reward-desc" className="text-sm font-medium text-foreground mb-2 block">
              Description (Optional)
            </Label>
            <Input
              id="reward-desc"
              placeholder="Enter reward description"
              value={newReward.description}
              onChange={(e) => setNewReward((s) => ({ ...s, description: e.target.value }))}
              className="w-full"
            />
          </div>
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setCurrentView('list')}>
              Cancel
            </Button>
            <Button variant='outline' onClick={handleAdd} disabled={!newReward.name?.trim()} className="px-6">
              Add Reward
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full p-4 flex flex-col h-[600px] ">
      <div className="flex items-center justify-between mb-4 px-1">
        <div className="text-lg font-medium text-foreground">Current Rewards ({local.rewards?.length || 0})</div>
        <Button onClick={() => setCurrentView('add')} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Reward
        </Button>
      </div>

        {local.rewards?.length ? (
            <ScrollArea className="h-full">
              <Table className=''>
                <TableHeader className="sticky top-0 bg-background ">
                  <TableRow>
                    <TableHead className="w-[25%]">Name</TableHead>
                    <TableHead className="w-[35%]">Description</TableHead>
                    <TableHead className="w-[15%]">Type</TableHead>
                    <TableHead className="w-[15%]">Drop Rate</TableHead>
                    <TableHead className="w-[10%]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {local.rewards.map((r: any) => (
                    <TableRow key={r.id}>
                      <TableCell className="font-medium">{r.name}</TableCell>
                      <TableCell className="text-muted-foreground">{r.description || '-'}</TableCell>
                      <TableCell>
                        <span className="text-xs px-2 py-1 bg-accent/50 rounded-md">
                          {r.type}
                        </span>
                      </TableCell>
                      <TableCell>{isGameRewardSet(local) ? `${r.dropRate}%` : `${(r.dropRate * 100).toFixed(0)}%`}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => handleRemove(r.id)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
        ) : (
          <div className="flex items-center justify-center h-full border border-dashed border-border rounded-lg">
            <div className="text-center text-muted-foreground">
              <div className="text-sm">No rewards in this set</div>
              <div className="text-xs mt-1">Click "Add Reward" to get started</div>
            </div>
          </div>
        )}
    </div>
  )
}

export default RewardSetManager
